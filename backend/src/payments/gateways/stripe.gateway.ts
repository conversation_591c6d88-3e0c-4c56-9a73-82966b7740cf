import { Injectable, Logger, BadRequestException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import Stripe from "stripe";
import {
  PaymentGateway,
  PaymentMethod,
  PaymentStatus,
  PaymentResult,
  RefundResult,
  StripePaymentParams,
  RazorpayPaymentParams,
  RefundParams,
  PaymentGatewayConfig,
} from "@shared/types";
import { IPaymentGateway } from "../interfaces/payment-gateway.interface";
import { createHmac } from "crypto";

@Injectable()
export class StripeGateway implements IPaymentGateway {
  private readonly logger = new Logger(StripeGateway.name);
  private stripe: Stripe | null = null;
  private readonly config: PaymentGatewayConfig;

  constructor(private configService: ConfigService) {
    this.config = {
      gateway: PaymentGateway.STRIPE,
      enabled: !!this.configService.get<string>("STRIPE_SECRET_KEY"),
      supportedCurrencies: ["USD", "EUR", "GBP", "CAD", "AUD", "SGD", "INR"],
      supportedMethods: [PaymentMethod.STRIPE, PaymentMethod.CARD],
      defaultCurrency: "USD",
      webhookUrl: "/webhooks/stripe",
    };
  }

  getConfig(): PaymentGatewayConfig {
    return this.config;
  }

  async initialize(): Promise<void> {
    const secretKey = this.configService.get<string>("STRIPE_SECRET_KEY");
    if (!secretKey) {
      this.logger.warn("Stripe secret key not configured");
      return;
    }

    try {
      this.stripe = new Stripe(secretKey, {
        apiVersion: "2025-04-30.basil",
        typescript: true,
      });

      // Test the connection
      await this.stripe.balance.retrieve();
      this.logger.log("Stripe gateway initialized successfully");
    } catch (error) {
      this.logger.error("Failed to initialize Stripe gateway:", error);
      throw new BadRequestException("Failed to initialize Stripe gateway");
    }
  }

  isConfigured(): boolean {
    return this.stripe !== null && this.config.enabled;
  }

  async createPaymentIntent(
    params: StripePaymentParams | RazorpayPaymentParams
  ): Promise<PaymentResult> {
    if (!this.isConfigured()) {
      throw new BadRequestException("Stripe is not configured");
    }

    const stripeParams = params as StripePaymentParams;

    try {
      const paymentIntent = await this.stripe!.paymentIntents.create({
        amount: Math.round(stripeParams.amount * 100), // Convert to cents
        currency: stripeParams.currency.toLowerCase(),
        ...(stripeParams.description && {
          description: stripeParams.description,
        }),
        ...(stripeParams.paymentMethodId && {
          payment_method: stripeParams.paymentMethodId,
        }),
        ...(stripeParams.customerId && { customer: stripeParams.customerId }),
        metadata: this.sanitizeMetadata(stripeParams.metadata || {}),
        confirmation_method: stripeParams.confirmationMethod || "automatic",
        capture_method: stripeParams.captureMethod || "automatic",
        setup_future_usage: stripeParams.setupFutureUsage,
      });

      return {
        success: true,
        transactionId: paymentIntent.id,
        orderId: paymentIntent.id,
        status: this.mapStripeStatusToPaymentStatus(paymentIntent.status),
        message: "Payment intent created successfully",
        gateway: PaymentGateway.STRIPE,
        paymentMethod: PaymentMethod.STRIPE,
        clientSecret: paymentIntent.client_secret || undefined,
        gatewayResponse: paymentIntent as unknown as Record<string, unknown>,
      };
    } catch (error) {
      this.logger.error("Stripe payment intent creation failed:", error);
      return {
        success: false,
        status: PaymentStatus.FAILED,
        message:
          error instanceof Error
            ? error.message
            : "Payment intent creation failed",
        gateway: PaymentGateway.STRIPE,
        paymentMethod: PaymentMethod.STRIPE,
      };
    }
  }

  async processPayment(
    params: StripePaymentParams | RazorpayPaymentParams
  ): Promise<PaymentResult> {
    if (!this.isConfigured()) {
      throw new BadRequestException("Stripe is not configured");
    }

    const stripeParams = params as StripePaymentParams;

    try {
      const paymentIntent = await this.stripe!.paymentIntents.create({
        amount: Math.round(stripeParams.amount * 100),
        currency: stripeParams.currency.toLowerCase(),
        ...(stripeParams.description && {
          description: stripeParams.description,
        }),
        ...(stripeParams.paymentMethodId && {
          payment_method: stripeParams.paymentMethodId,
        }),
        ...(stripeParams.customerId && { customer: stripeParams.customerId }),
        metadata: this.sanitizeMetadata(stripeParams.metadata || {}),
        confirm: true,
        confirmation_method: stripeParams.confirmationMethod || "automatic",
        capture_method: stripeParams.captureMethod || "automatic",
      });

      return {
        success:
          paymentIntent.status === "succeeded" ||
          paymentIntent.status === "requires_capture",
        transactionId: paymentIntent.id,
        orderId: paymentIntent.id,
        status: this.mapStripeStatusToPaymentStatus(paymentIntent.status),
        message: "Payment processed successfully",
        gateway: PaymentGateway.STRIPE,
        paymentMethod: PaymentMethod.STRIPE,
        clientSecret: paymentIntent.client_secret || undefined,
        gatewayResponse: paymentIntent as unknown as Record<string, unknown>,
      };
    } catch (error) {
      this.logger.error("Stripe payment processing failed:", error);
      return {
        success: false,
        status: PaymentStatus.FAILED,
        message:
          error instanceof Error ? error.message : "Payment processing failed",
        gateway: PaymentGateway.STRIPE,
        paymentMethod: PaymentMethod.STRIPE,
      };
    }
  }

  async capturePayment(
    paymentId: string,
    amount?: number
  ): Promise<PaymentResult> {
    if (!this.isConfigured()) {
      throw new BadRequestException("Stripe is not configured");
    }

    try {
      const captureParams: Stripe.PaymentIntentCaptureParams = {};
      if (amount) {
        captureParams.amount_to_capture = Math.round(amount * 100);
      }

      const paymentIntent = await this.stripe!.paymentIntents.capture(
        paymentId,
        captureParams
      );

      return {
        success: paymentIntent.status === "succeeded",
        transactionId: paymentIntent.id,
        orderId: paymentIntent.id,
        status: this.mapStripeStatusToPaymentStatus(paymentIntent.status),
        message: "Payment captured successfully",
        gateway: PaymentGateway.STRIPE,
        paymentMethod: PaymentMethod.STRIPE,
        gatewayResponse: paymentIntent as unknown as Record<string, unknown>,
      };
    } catch (error) {
      this.logger.error("Stripe payment capture failed:", error);
      return {
        success: false,
        status: PaymentStatus.FAILED,
        message:
          error instanceof Error ? error.message : "Payment capture failed",
        gateway: PaymentGateway.STRIPE,
        paymentMethod: PaymentMethod.STRIPE,
      };
    }
  }

  async refundPayment(params: RefundParams): Promise<RefundResult> {
    if (!this.isConfigured()) {
      throw new BadRequestException("Stripe is not configured");
    }

    try {
      const refundParams: Stripe.RefundCreateParams = {
        payment_intent: params.transactionId,
        metadata: this.sanitizeMetadata(params.metadata || {}),
      };

      if (params.amount) {
        refundParams.amount = Math.round(params.amount * 100);
      }

      if (params.reason) {
        refundParams.reason = params.reason as Stripe.RefundCreateParams.Reason;
      }

      const refund = await this.stripe!.refunds.create(refundParams);

      return {
        success: refund.status === "succeeded",
        refundId: refund.id,
        amount: refund.amount / 100,
        status: refund.status as "pending" | "succeeded" | "failed",
        gateway: PaymentGateway.STRIPE,
        gatewayResponse: refund as unknown as Record<string, unknown>,
      };
    } catch (error) {
      this.logger.error("Stripe refund failed:", error);
      throw new BadRequestException(
        `Refund failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  verifyWebhookSignature(
    payload: string,
    signature: string,
    secret: string
  ): boolean {
    try {
      const expectedSignature = createHmac("sha256", secret)
        .update(payload)
        .digest("hex");

      return signature === `sha256=${expectedSignature}`;
    } catch (error) {
      this.logger.error("Webhook signature verification failed:", error);
      return false;
    }
  }

  async getPaymentDetails(transactionId: string): Promise<PaymentResult> {
    if (!this.isConfigured()) {
      throw new BadRequestException("Stripe is not configured");
    }

    try {
      const paymentIntent = await this.stripe!.paymentIntents.retrieve(
        transactionId
      );

      return {
        success: true,
        transactionId: paymentIntent.id,
        orderId: paymentIntent.id,
        status: this.mapStripeStatusToPaymentStatus(paymentIntent.status),
        message: "Payment details retrieved successfully",
        gateway: PaymentGateway.STRIPE,
        paymentMethod: PaymentMethod.STRIPE,
        gatewayResponse: paymentIntent as unknown as Record<string, unknown>,
      };
    } catch (error) {
      this.logger.error("Failed to retrieve Stripe payment details:", error);
      throw new BadRequestException("Failed to retrieve payment details");
    }
  }

  getSupportedCurrencies(): string[] {
    return this.config.supportedCurrencies;
  }

  getSupportedPaymentMethods(): string[] {
    return this.config.supportedMethods;
  }

  isCurrencySupported(currency: string): boolean {
    return this.config.supportedCurrencies.includes(currency.toUpperCase());
  }

  isPaymentMethodSupported(method: string): boolean {
    return this.config.supportedMethods.includes(method as PaymentMethod);
  }

  private mapStripeStatusToPaymentStatus(stripeStatus: string): PaymentStatus {
    switch (stripeStatus) {
      case "succeeded":
        return PaymentStatus.COMPLETED;
      case "processing":
        return PaymentStatus.PROCESSING;
      case "requires_payment_method":
      case "requires_confirmation":
      case "requires_action":
        return PaymentStatus.PENDING;
      case "canceled":
        return PaymentStatus.CANCELLED;
      case "requires_capture":
        return PaymentStatus.PROCESSING;
      default:
        return PaymentStatus.PENDING;
    }
  }

  private sanitizeMetadata(
    metadata: Record<string, unknown>
  ): Record<string, string> {
    const sanitized: Record<string, string> = {};
    for (const [key, value] of Object.entries(metadata)) {
      if (value !== null && value !== undefined) {
        sanitized[key] = String(value);
      }
    }
    return sanitized;
  }
}
