import {
  <PERSON>,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Request,
  ForbiddenException,
  Query,
  Inject,
} from "@nestjs/common";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { UserRole } from "@shared/types";
import { RatingsService } from "./ratings.service";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";

@ApiTags("ratings")
@Controller("ratings")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class RatingsController {
  constructor(
    @Inject(RatingsService)
    private readonly ratingsService: RatingsService
  ) {}

  @Post()
  @ApiOperation({ summary: "Create a new rating" })
  @ApiResponse({ status: 201, description: "Rating created successfully" })
  async createRating(@Body() createRatingDto: any, @Request() req: any) {
    return this.ratingsService.create(createRatingDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: "Get all ratings" })
  @ApiResponse({ status: 200, description: "Returns all ratings" })
  @ApiQuery({ name: "page", required: false, type: Number })
  @ApiQuery({ name: "limit", required: false, type: Number })
  async findAll(
    @Query("page") page: number = 1,
    @Query("limit") limit: number = 10,
    @Query() filters: any
  ) {
    return this.ratingsService.findAll(page, limit, filters);
  }

  @Get("user/:userId")
  @ApiOperation({ summary: "Get ratings for a specific user" })
  @ApiResponse({ status: 200, description: "Returns user ratings" })
  @ApiParam({ name: "userId", description: "ID of the user" })
  async findByUser(@Param("userId") userId: string) {
    return this.ratingsService.findByUser(userId);
  }

  @Get("job/:jobId")
  @ApiOperation({ summary: "Get ratings for a specific job" })
  @ApiResponse({ status: 200, description: "Returns job ratings" })
  @ApiParam({ name: "jobId", description: "ID of the job" })
  async findByJob(@Param("jobId") jobId: string) {
    return this.ratingsService.findByJob(jobId);
  }

  @Get("received")
  @ApiOperation({ summary: "Get ratings received by the current user" })
  @ApiResponse({
    status: 200,
    description: "Returns ratings received by the user",
  })
  @ApiQuery({ name: "limit", required: false, type: Number })
  @ApiQuery({ name: "offset", required: false, type: Number })
  async getReceivedRatings(
    @Request() req,
    @Query("limit") limit: number = 10,
    @Query("offset") offset: number = 0
  ) {
    // This is a placeholder implementation
    // In a real application, you would query the database for ratings
    return {
      ratings: [
        {
          id: "1",
          rating: 4.5,
          comment: "Great worker, very professional",
          createdAt: new Date().toISOString(),
          job: {
            id: "job1",
            title: "Plumbing Work",
          },
          ratedBy: {
            id: "user1",
            name: "John Doe",
            profilePic: "https://example.com/profile.jpg",
          },
        },
      ],
      total: 1,
      limit,
      offset,
    };
  }

  @Get("given")
  @ApiOperation({ summary: "Get ratings given by the current user" })
  @ApiResponse({
    status: 200,
    description: "Returns ratings given by the user",
  })
  @ApiQuery({ name: "limit", required: false, type: Number })
  @ApiQuery({ name: "offset", required: false, type: Number })
  async getGivenRatings(
    @Request() req,
    @Query("limit") limit: number = 10,
    @Query("offset") offset: number = 0
  ) {
    // This is a placeholder implementation
    // In a real application, you would query the database for ratings
    return {
      ratings: [
        {
          id: "2",
          rating: 5,
          comment: "Excellent company, would work with them again",
          createdAt: new Date().toISOString(),
          job: {
            id: "job2",
            title: "Electrical Work",
          },
          ratedUser: {
            id: "company1",
            name: "ABC Company",
            profilePic: "https://example.com/company.jpg",
          },
        },
      ],
      total: 1,
      limit,
      offset,
    };
  }

  @Get("summary")
  @ApiOperation({ summary: "Get ratings summary for the current user" })
  @ApiResponse({ status: 200, description: "Returns ratings summary" })
  async getRatingsSummary(@Request() req) {
    // This is a placeholder implementation
    // In a real application, you would calculate this from the database
    return {
      averageRating: 4.5,
      totalRatings: 10,
      ratingCounts: {
        1: 0,
        2: 1,
        3: 2,
        4: 3,
        5: 4,
      },
      categories: {
        punctuality: {
          averageRating: 4.7,
          totalRatings: 10,
        },
        communication: {
          averageRating: 4.3,
          totalRatings: 10,
        },
        quality: {
          averageRating: 4.5,
          totalRatings: 10,
        },
      },
    };
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a specific rating by ID" })
  @ApiResponse({ status: 200, description: "Returns the rating" })
  @ApiParam({ name: "id", description: "ID of the rating" })
  async findOne(@Param("id") id: string) {
    return this.ratingsService.findOne(id);
  }
}
