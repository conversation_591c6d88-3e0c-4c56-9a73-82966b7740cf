import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Request,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Inject,
} from "@nestjs/common";
import { Request as ExpressRequest } from "express";
import { FileInterceptor } from "@nestjs/platform-express";
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { CompaniesService } from "./companies.service";
import { CreateCompanyDto } from "./dto/create-company.dto";
import { UpdateCompanyDto } from "./dto/update-company.dto";
import { QueryCompaniesDto } from "./dto/query-companies.dto";
import { UpdateCompanyKycDto } from "./dto/update-company-kyc.dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { UserRole } from "../common/enums/user-role.enum";
import { DocumentType } from "../common/enums/document-type.enum";
import { VerificationStatus } from "../common/enums/verification-status.enum";
import { ApiPaginatedResponse } from "../common/decorators/api-paginated-response.decorator";
import { Company } from "./entities/company.entity";
import {
  CreateCompanySwaggerDto,
  UpdateCompanySwaggerDto,
  UpdateCompanyKycSwaggerDto,
  CompanyResponseSwaggerDto,
} from "./dto/swagger.dto";
import { DocumentResponseSwaggerDto } from "../users/dto/swagger.dto";

@ApiTags("companies")
@Controller("companies")
export class CompaniesController {
  constructor(
    @Inject(CompaniesService)
    private readonly companiesService: CompaniesService
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.COMPANY)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Create a company profile" })
  @ApiBody({ type: CreateCompanySwaggerDto })
  @ApiResponse({
    status: 201,
    description: "Company created successfully",
    type: CompanyResponseSwaggerDto,
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden" })
  create(
    @Request() req: ExpressRequest & { user: any },
    @Body() createCompanyDto: CreateCompanyDto
  ) {
    return this.companiesService.create(req.user.id, createCompanyDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get all companies (Admin only)" })
  @ApiPaginatedResponse(Company)
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden" })
  findAll(@Query() queryCompaniesDto: QueryCompaniesDto) {
    return this.companiesService.findAll(queryCompaniesDto);
  }

  @Get("profile")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.COMPANY)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get company profile for the authenticated user" })
  @ApiResponse({
    status: 200,
    description: "Company profile found",
    type: CompanyResponseSwaggerDto,
  })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden" })
  @ApiResponse({ status: 404, description: "Company not found" })
  findProfile(@Request() req: ExpressRequest & { user: any }) {
    return this.companiesService.findByUserId(req.user.id);
  }

  @Get(":id")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get company by ID" })
  @ApiParam({ name: "id", description: "Company ID" })
  @ApiResponse({
    status: 200,
    description: "Company found",
    type: CompanyResponseSwaggerDto,
  })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 404, description: "Company not found" })
  findOne(@Param("id") id: string) {
    return this.companiesService.findOne(id);
  }

  @Patch(":id")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.COMPANY, UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Update company" })
  @ApiParam({ name: "id", description: "Company ID" })
  @ApiBody({ type: UpdateCompanySwaggerDto })
  @ApiResponse({
    status: 200,
    description: "Company updated successfully",
    type: CompanyResponseSwaggerDto,
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden" })
  @ApiResponse({ status: 404, description: "Company not found" })
  update(
    @Param("id") id: string,
    @Body() updateCompanyDto: UpdateCompanyDto,
    @Request() req: ExpressRequest & { user: any }
  ) {
    return this.companiesService.updateWithPermissionCheck(
      id,
      updateCompanyDto,
      req.user.id,
      req.user.role
    );
  }

  @Delete(":id")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Delete company (Admin only)" })
  @ApiParam({ name: "id", description: "Company ID" })
  @ApiResponse({ status: 200, description: "Company deleted successfully" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden" })
  @ApiResponse({ status: 404, description: "Company not found" })
  remove(@Param("id") id: string) {
    return this.companiesService.remove(id);
  }

  @Patch("kyc")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.COMPANY)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Update company KYC information" })
  @ApiBody({ type: UpdateCompanyKycSwaggerDto })
  @ApiResponse({
    status: 200,
    description: "KYC information updated successfully",
    type: CompanyResponseSwaggerDto,
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden" })
  updateKyc(
    @Request() req: ExpressRequest & { user: any },
    @Body() updateCompanyKycDto: UpdateCompanyKycDto
  ) {
    // Get company by user ID
    return this.companiesService
      .findByUserId(req.user.id)
      .then((company) =>
        this.companiesService.updateKyc(company.id, updateCompanyKycDto)
      );
  }

  @Post("documents")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.COMPANY)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Upload company document" })
  @ApiConsumes("multipart/form-data")
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        file: {
          type: "string",
          format: "binary",
        },
        type: {
          type: "string",
          enum: Object.values(DocumentType),
        },
        documentNumber: {
          type: "string",
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: "Document uploaded successfully",
    type: DocumentResponseSwaggerDto,
  })
  @UseInterceptors(FileInterceptor("file"))
  uploadDocument(
    @Request() req: ExpressRequest & { user: any },
    @UploadedFile() file: Express.Multer.File,
    @Body("type") type: DocumentType,
    @Body("documentNumber") documentNumber?: string
  ) {
    return this.companiesService.processAndUploadDocument(
      req.user.id,
      file,
      type,
      documentNumber
    );
  }

  @Patch("documents/:id/verify")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Verify company document (Admin only)" })
  @ApiParam({ name: "id", description: "Document ID" })
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        status: {
          type: "string",
          enum: Object.values(VerificationStatus),
        },
        rejectionReason: {
          type: "string",
        },
      },
      required: ["status"],
    },
  })
  @ApiResponse({
    status: 200,
    description: "Document verified successfully",
    type: DocumentResponseSwaggerDto,
  })
  verifyDocument(
    @Param("id") id: string,
    @Request() req: ExpressRequest & { user: any },
    @Body("status") status: VerificationStatus,
    @Body("rejectionReason") rejectionReason?: string
  ) {
    return this.companiesService.verifyDocumentWithValidation(
      id,
      req.user.id,
      status,
      rejectionReason
    );
  }
}
