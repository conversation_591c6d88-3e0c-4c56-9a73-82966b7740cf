import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import { Document } from "../users/entities/document.entity";
import { UsersService } from "../users/users.service";
import { NotificationsService } from "../notifications/notifications.service";
import { ActivityLogService } from "../activity-log/activity-log.service";
import { UserRole } from "@shared/types";
import { VerificationStatus } from "src/common/enums/verification-status.enum";
import { DocumentType } from "src/common/enums/document-type.enum";

@Injectable()
export class DocumentsService {
  constructor(
    @InjectRepository(Document)
    private documentsRepository: Repository<Document>,
    @Inject(UsersService)
    private readonly usersService: UsersService,
    @Inject(NotificationsService)
    private readonly notificationsService: NotificationsService,
    @Inject(ActivityLogService)
    private readonly activityLogService: ActivityLogService
  ) {}

  async uploadDocument(
    userId: string,
    documentType: DocumentType,
    documentUrl: string,
    documentNumber?: string
  ): Promise<Document> {
    const user = await this.usersService.findOne(userId);

    // Check if document of this type already exists
    const existingDoc = await this.documentsRepository.findOne({
      where: {
        userId,
        documentType,
      },
    });

    if (existingDoc) {
      // Update existing document
      existingDoc.documentUrl = documentUrl;
      if (documentNumber) {
        existingDoc.documentNumber = documentNumber;
      }
      existingDoc.verificationStatus = VerificationStatus.PENDING;
      existingDoc.verifiedBy = null as any;
      existingDoc.verifiedAt = null as any;
      existingDoc.rejectionReason = null as any;

      const updatedDoc = await this.documentsRepository.save(existingDoc);

      // Log activity
      await this.activityLogService.logActivity({
        userId,
        action: "update_document",
        entityType: "document",
        entityId: updatedDoc.id,
        description: `Updated ${documentType} document`,
      });

      // Notify admins
      const admins = await this.usersService.findAll({ role: UserRole.ADMIN });
      for (const admin of admins.data) {
        //TODO: implement the pagination
        await this.notificationsService.create({
          userId: admin.id,
          title: "Document Updated",
          message: `${user.name} has updated their ${documentType} document`,
          type: "document",
          metadata: { documentId: updatedDoc.id, userId },
          link: `/admin/users/${userId}/documents`,
        });
      }

      return updatedDoc;
    }

    // Create new document
    const documentData: any = {
      userId,
      documentType,
      documentUrl,
    };

    if (documentNumber) {
      documentData.documentNumber = documentNumber;
    }

    const document = this.documentsRepository.create(documentData);
    const savedDocument = await this.documentsRepository.save(document);

    // Log activity
    await this.activityLogService.logActivity({
      userId,
      action: "upload_document",
      entityType: "document",
      entityId: savedDocument.id,
      description: `Uploaded ${documentType} document`,
    });

    // Notify admins (limit to first 10 to avoid performance issues)
    const admins = await this.usersService.findAll({
      role: UserRole.ADMIN,
      page: 1,
      limit: 10,
    });
    for (const admin of admins.data) {
      await this.notificationsService.create({
        userId: admin.id,
        title: "New Document Uploaded",
        message: `${user.name} has uploaded a new ${documentType} document`,
        type: "document",
        metadata: { documentId: savedDocument.id, userId },
        link: `/admin/users/${userId}/documents`,
      });
    }

    return savedDocument;
  }

  async verifyDocument(
    documentId: string,
    adminId: string,
    isApproved: boolean,
    rejectionReason?: string
  ): Promise<Document> {
    const document = await this.documentsRepository.findOne({
      where: { id: documentId },
      relations: ["user"],
    });

    if (!document) {
      throw new NotFoundException(`Document with ID ${documentId} not found`);
    }

    // Verify admin
    const admin = await this.usersService.findOne(adminId);
    if (admin.role !== UserRole.ADMIN) {
      throw new BadRequestException("Only admins can verify documents");
    }

    document.verificationStatus = isApproved
      ? VerificationStatus.VERIFIED
      : VerificationStatus.REJECTED;
    document.verifiedBy = adminId;
    document.verifiedAt = new Date();

    if (!isApproved && rejectionReason) {
      document.rejectionReason = rejectionReason;
    }

    const savedDocument = await this.documentsRepository.save(document);

    // Log activity
    await this.activityLogService.logActivity({
      userId: adminId,
      action: isApproved ? "verify_document" : "reject_document",
      entityType: "document",
      entityId: documentId,
      description: isApproved
        ? `Verified ${document.documentType} document for user ${
            document.user?.name || "Unknown"
          }`
        : `Rejected ${document.documentType} document for user ${
            document.user?.name || "Unknown"
          }: ${rejectionReason}`,
    });

    // Notify user
    if (document.userId) {
      await this.notificationsService.create({
        userId: document.userId,
        title: isApproved ? "Document Verified" : "Document Rejected",
        message: isApproved
          ? `Your ${document.documentType} document has been verified`
          : `Your ${document.documentType} document was rejected: ${rejectionReason}`,
        type: "document",
        metadata: { documentId },
        link: `/profile/documents`,
      });
    }

    // Update user's KYC status if needed
    if (isApproved && document.userId) {
      await this.checkAndUpdateKycStatus(document.userId);
    }

    return savedDocument;
  }

  async getUserDocuments(userId: string): Promise<Document[]> {
    return this.documentsRepository.find({
      where: { userId },
      order: { createdAt: "DESC" },
    });
  }

  async getAllPendingDocuments(): Promise<Document[]> {
    return this.documentsRepository.find({
      where: { verificationStatus: VerificationStatus.PENDING },
      relations: ["user"],
      order: { createdAt: "ASC" },
    });
  }

  private async checkAndUpdateKycStatus(userId: string): Promise<void> {
    const user = await this.usersService.findOne(userId);

    // Define required documents based on user role
    const requiredDocTypes: DocumentType[] =
      user.role === UserRole.WORKER
        ? [DocumentType.ID_PROOF, DocumentType.ADDRESS_PROOF]
        : [DocumentType.COMPANY_REGISTRATION, DocumentType.TAX_DOCUMENT];

    // Check if all required documents are verified
    const verifiedDocs = await this.documentsRepository.find({
      where: {
        userId,
        documentType: requiredDocTypes[0], //TODO: fix this,
        verificationStatus: VerificationStatus.VERIFIED,
      },
    });

    // If all required documents are verified, update KYC status
    if (verifiedDocs.length === requiredDocTypes.length) {
      // Update KYC status
      await this.usersService.update(userId, { isKycVerified: true });
    }
  }
}
