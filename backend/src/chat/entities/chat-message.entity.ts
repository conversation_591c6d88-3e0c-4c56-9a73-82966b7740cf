import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  Jo<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Index,
} from "typeorm";

import { GenericChat } from "./generic-chat.entity";
import { Chat } from "./chat.entity";
import { User } from "src/users/entities/user.entity";
import { MessageType, MessageStatus } from "../enums/chat-type.enum";

// Keep old enum for backward compatibility
export enum SenderType {
  WORKER = "worker",
  COMPANY = "company",
}

@Entity("chat_messages")
export class ChatMessage {
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @ManyToOne(() => GenericChat, (chat) => chat.messages, { nullable: true })
  @JoinColumn({ name: "genericChatId" })
  genericChat?: GenericChat;

  @Column({ nullable: true })
  @Index()
  genericChatId?: string;

  // Keep for backward compatibility with legacy Chat entity
  @ManyToOne(() => Cha<PERSON>, (chat) => chat.messages, { nullable: true })
  @JoinColumn({ name: "chatId" })
  chat?: Chat;

  @Column({ nullable: true })
  @Index()
  chatId?: string;

  @Column({
    type: "enum",
    enum: MessageType,
    default: MessageType.TEXT,
  })
  messageType!: MessageType;

  @Column("text")
  message!: string;

  @Column({ nullable: true })
  fileUrl?: string;

  @Column({ nullable: true })
  fileName?: string;

  @Column({ nullable: true })
  fileSize?: number;

  @Column({ nullable: true })
  fileMimeType?: string;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: "senderId" })
  sender!: User;

  @Column()
  @Index()
  senderId!: string;

  // Keep for backward compatibility
  @Column({
    type: "enum",
    enum: SenderType,
    nullable: true,
  })
  senderType?: SenderType;

  @Column({
    type: "enum",
    enum: MessageStatus,
    default: MessageStatus.SENT,
  })
  status!: MessageStatus;

  @Column({ default: false })
  isRead!: boolean;

  @Column({ nullable: true })
  readAt?: Date;

  @Column({ type: "jsonb", nullable: true })
  metadata?: Record<string, any>;

  @Column({ nullable: true })
  replyToId?: string;

  @ManyToOne(() => ChatMessage, { nullable: true })
  @JoinColumn({ name: "replyToId" })
  replyTo?: ChatMessage;

  @Column({ default: false })
  isEdited!: boolean;

  @Column({ nullable: true })
  editedAt?: Date;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
